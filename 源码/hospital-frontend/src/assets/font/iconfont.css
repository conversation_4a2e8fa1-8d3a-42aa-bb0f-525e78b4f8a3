@font-face {
  font-family: "iconfont"; /* Project id 4336417 */
  src: url('iconfont.woff2?t=1700389625174') format('woff2'),
  url('iconfont.woff?t=1700389625174') format('woff'),
  url('iconfont.ttf?t=1700389625174') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*登录图标*/
.icon-login-user:before {
  content: "\e657";
}

/*登录密码*/
.icon-login-password:before {
  content: "\e81f";
}

/*登录按钮*/
.icon-login-button:before {
  content: "\e815";
}

/*注册按钮*/
.icon-login-register:before {
  content: "\e64a";
}

/*取消按钮*/
.icon-cancel-button:before {
  content: "\e642";
}

/*确认按钮*/
.icon-sure-button:before {
  content: "\e809";
}

/*首页 -菜单 */
.icon-homepage-menu:before {
  content: "\e650";
}

/*医生用户 - 菜单*/
.icon-doctor-menu:before {
  content: "\e64f";
}

/*患者用户 - 菜单*/
.icon-patient-menu:before {
  content: "\e632";
}

/*挂号单 - 菜单*/
.icon-orders-menu:before {
  content: "\e810";
}

/*药物 - 菜单*/
.icon-drug-menu:before {
  content: "\e636";
}

/*检查项目 - 菜单*/
.icon-checks-menu:before {
  content: "\e638";
}

/*病床分配 - 菜单*/
.icon-bed-menu:before {
  content: "\e63c";
}

/*医生排班 - 菜单*/
.icon-arrange-menu:before {
  content: "\e80a";
}

/*统计分析 - 菜单*/
.icon-count-menu:before {
  content: "\e827";
}

/*搜索按钮*/
.icon-search-button:before {
  content: "\e61c";
}

/*添加按钮*/
.icon-add-button:before {
  content: "\e813";
}

/*编辑按钮*/
.icon-edit-button:before {
  content: "\e61e";
}

/*删除按钮*/
.icon-delete-button:before {
  content: "\e65b";
}

/*确认按钮*/
.icon-sure-button:before {
  content: "\e622";
}

/*取消按钮*/
.icon-cancel-button:before {
  content: "\e8e7";
}

/*导出按钮*/
.icon-export-button:before {
  content: "\e824";
}

/*诊断按钮*/
.icon-diagnosis-button:before {
  content: "\e607";
}

/*住院按钮*/
.icon-hotel-button:before {
  content: "\e619";
}

/*处理按钮*/
.icon-deal-button:before {
  content: "\e812";
}

/*挂号按钮*/
.icon-register-button:before {
  content: "\e636";
}

/*医生列表*/
.icon-doctor-list:before {
  content: "\e618";
}

/*内科*/
.icon-inner:before {
  content: "\e671";
}




