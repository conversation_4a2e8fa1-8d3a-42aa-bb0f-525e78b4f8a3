<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shanzhu.hospital.mapper.PatientUserMapper">
    <select id="patientAge" resultType="Integer">
        select count(p_id) as agePeople from patient_user  where
            p_age BETWEEN #{startAge} AND #{endAge}
    </select>


</mapper>