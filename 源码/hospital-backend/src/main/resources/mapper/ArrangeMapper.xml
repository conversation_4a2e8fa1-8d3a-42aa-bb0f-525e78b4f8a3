<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shanzhu.hospital.mapper.ArrangeMapper">

    <resultMap id="arrangeDoctorMap" type="com.shanzhu.hospital.entity.po.Arrange">

        <id property="arId" column="ar_id" />
        <result property="arTime" column="ar_time" />
        <result property="dId" column="d_id" />

        <association property="doctor" javaType="com.shanzhu.hospital.entity.po.Doctor">
            <result property="dId" column="d_id" />
            <result property="dName" column="d_name"/>
            <result property="dGender" column="d_gender"/>
            <result property="dPost" column="d_post"/>
            <result property="dIntroduction" column="d_introduction"/>
            <result property="dSection" column="d_section"/>
            <result property="dPrice" column="d_price"/>
            <result property="dAvgStar" column="d_avg_star"/>
        </association>
    </resultMap>

    <select id="findByTime" resultMap="arrangeDoctorMap">
        select * from doctor_user d,arrange a where d.d_id=a.d_id and a.ar_time=#{ar_time} and d.d_section=#{d_section}
    </select>

</mapper>