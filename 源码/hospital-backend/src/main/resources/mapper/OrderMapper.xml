<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shanzhu.hospital.mapper.OrderMapper">

    <resultMap id="orderPatientMap" type="com.shanzhu.hospital.entity.po.Orders">
        <id property="oId" column="o_id" />
        <result property="pId" column="p_id" />
        <result property="dId" column="d_id" />
        <result property="oStart" column="o_start" />
        <result property="oState" column="d_state" />
        <result property="countGender" column="countGender"/>

        <association property="patient" javaType="com.shanzhu.hospital.entity.po.Patient">
            <result property="pId" column="p_id" />
            <result property="pGender" column="p_gender"/>
        </association>
    </resultMap>

    <select id="orderPeople" resultType="Integer" parameterType="String">
        select count(p_id) as countGender from orders where o_start like #{o_start}"%"
    </select>

    <select id="orderPeopleByDid" resultType="Integer">
        select count(p_id) from orders where o_start like #{o_start}"%" and d_id=#{d_id}
    </select>


    <select id="orderGender" resultMap="orderPatientMap">
        select p_gender,count(p_gender) as countGender from patient_user p,orders o
        where p.p_id=o.p_id GROUP BY p_gender
    </select>


    <resultMap id="orderOidMap" type="com.shanzhu.hospital.entity.po.Orders">
        <id property="oId" column="o_id" />
        <result property="pId" column="p_id" />
        <result property="dId" column="d_id" />
        <result property="oStart" column="o_start" />
        <result property="oEnd" column="o_end" />
        <result property="oRecord" column="o_record"/>
        <result property="oDrug" column="o_drug"/>
        <result property="oCheck" column="o_check"/>
        <result property="oTotalPrice" column="o_total_price"/>
        <result property="oAdvice" column="o_advice"/>

        <association property="patient" javaType="com.shanzhu.hospital.entity.po.Patient">
            <result property="pId" column="p_id" />
            <result property="pName" column="p_name"/>
            <result property="pPhone" column="p_phone"/>
            <result property="pEmail" column="p_email"/>
            <result property="pGender" column="p_gender"/>
            <result property="pAge" column="p_age"/>
        </association>

    </resultMap>

    <select id="findOrderByOid" resultMap="orderOidMap">
        select * from orders o,patient_user p where o.p_id = p.p_id and o.o_id=#{o_id}
    </select>

    <update id="updateOrderByAdd" parameterType="com.shanzhu.hospital.entity.po.Orders">
        update orders set o_check = concat(o_check,#{oCheck}),o_drug = concat(o_drug,#{oDrug}),o_total_price = #{oTotalPrice},o_advice = #{oAdvice} where o_id=#{oId}
    </update>


    <resultMap id="orderSectionMap" type="com.shanzhu.hospital.entity.po.Orders">
        <id property="oId" column="o_id" />
        <result property="dId" column="d_id" />
        <result property="oStart" column="o_start" />
        <result property="countSection" column="countSection"/>

        <association property="doctor" javaType="com.shanzhu.hospital.entity.po.Doctor">
            <result property="dId" column="d_id" />
            <result property="dSection" column="d_section"/>
        </association>

    </resultMap>

    <select id="orderSection" resultMap="orderSectionMap">
        SELECT d.d_section,count(o.d_id) as countSection from orders o,doctor_user d where o.d_id=d.d_id
        AND o_start BETWEEN #{startTime} and #{endTime}
        GROUP BY d.d_section
    </select>

    <resultMap id="orderPatientDoctorMap" type="com.shanzhu.hospital.entity.po.Orders">
        <id property="oId" column="o_id" />
        <result property="pId" column="p_id" />
        <result property="dId" column="d_id" />
        <result property="oStart" column="o_start" />
        <result property="oEnd" column="o_end" />
        <result property="oTotalPrice" column="o_total_price" />
        <result property="oPriceState" column="o_price_state" />
        <result property="oState" column="o_state" />
        <result property="dName" column="dName" />
        <result property="pName" column="pName" />

        <association property="doctor" javaType="com.shanzhu.hospital.entity.po.Doctor">
            <result property="dId" column="d_id" />
            <result property="dName" column="d_name" />
        </association>

        <association property="patient" javaType="com.shanzhu.hospital.entity.po.Patient">
            <result property="pId" column="p_id" />
            <result property="pName" column="p_name" />
        </association>
    </resultMap>

    <select id="findOrderByNull" resultMap="orderPatientDoctorMap">
        SELECT o.o_id,d.d_name as dName,o.p_id,p.p_name as pName,o.o_start
        from orders o JOIN doctor_user d JOIN patient_user p
        on o.d_id=#{dId}
        and o.d_id=d.d_id
        and o.p_id=p.p_id
        and o.o_start like #{oStart}"%"
        and o.o_state=0
        ORDER BY o.o_start
    </select>

    <select id="findOrderByPid" resultMap="orderPatientDoctorMap">
        SELECT o.o_id,o.d_id,d.d_name as dName,o.p_id,p.p_name as pName,o.o_start,o.o_end,o.o_total_price,o.o_price_state,o.o_state
        from orders o JOIN doctor_user d JOIN patient_user p
        on o.p_id=#{pId}
        and o.d_id=d.d_id
        and o.p_id=p.p_id
        ORDER BY o.o_end DESC
    </select>

</mapper>